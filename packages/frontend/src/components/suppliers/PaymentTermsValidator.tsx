'use client';

import { useEffect, useState } from 'react';
import { AlertTriangle, CheckCircle, Clock, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { formatCurrency } from '@/lib/utils';
import { 
  CreateSupplierPaymentDto, 
  PaymentTermsValidation, 
  PaymentTermsCalculation 
} from '@/types/supplier';
import { useValidatePaymentTerms, useCalculatePaymentTerms } from '@/hooks/useSuppliers';

interface PaymentTermsValidatorProps {
  supplierId: string;
  paymentData: CreateSupplierPaymentDto;
  onValidationChange: (isValid: boolean, calculation?: PaymentTermsCalculation) => void;
  className?: string;
}

export function PaymentTermsValidator({
  supplierId,
  paymentData,
  onValidationChange,
  className,
}: PaymentTermsValidatorProps) {
  const [validationResult, setValidationResult] = useState<PaymentTermsValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const validateMutation = useValidatePaymentTerms();
  
  // Auto-calculate payment terms when purchase order changes
  const { data: calculationData, isLoading: isCalculating } = useCalculatePaymentTerms(
    supplierId,
    paymentData.purchaseOrderId,
    paymentData.effectivePaymentTerms
  );

  // Validate payment terms when data changes
  const validatePaymentTerms = async () => {
    // Only validate if we have the minimum required data
    if (!supplierId || !paymentData.amount || !paymentData.paymentDate) {
      setValidationResult(null);
      onValidationChange(true); // Allow validation to pass if no data to validate
      return;
    }

    // Only run validation if there's something to validate:
    // 1. Purchase order is selected (needs PO-specific validation)
    // 2. Manual payment terms are provided (needs terms validation)
    // 3. Otherwise, it's a simple supplier payment - no validation needed
    const needsValidation = paymentData.purchaseOrderId ||
                           (paymentData.effectivePaymentTerms !== undefined && paymentData.effectivePaymentTerms !== null);

    if (!needsValidation) {
      setValidationResult(null);
      onValidationChange(true); // Simple supplier payment - no validation required
      return;
    }

    setIsValidating(true);
    try {
      const result = await validateMutation.mutateAsync({
        supplierId,
        paymentData,
      });
      setValidationResult(result);
      onValidationChange(result.valid, result.paymentTermsCalculation);
    } catch (error) {
      console.error('Payment terms validation failed:', error);
      setValidationResult({
        valid: false,
        error: 'Gagal memvalidasi termin pembayaran',
        message: 'Terjadi kesalahan saat memvalidasi termin pembayaran',
      });
      onValidationChange(false);
    } finally {
      setIsValidating(false);
    }
  };

  // Auto-validate when payment data changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Only auto-validate if validation is needed
      const needsValidation = paymentData.purchaseOrderId ||
                             (paymentData.effectivePaymentTerms !== undefined && paymentData.effectivePaymentTerms !== null);

      if (needsValidation && paymentData.amount && paymentData.paymentDate) {
        validatePaymentTerms();
      } else if (!needsValidation) {
        // Clear validation for simple supplier payments
        setValidationResult(null);
        onValidationChange(true);
      }
    }, 500); // Debounce validation

    return () => clearTimeout(timeoutId);
  }, [
    supplierId,
    paymentData.amount,
    paymentData.paymentDate,
    paymentData.purchaseOrderId,
    paymentData.goodsReceiptId,
    paymentData.effectivePaymentTerms,
  ]);

  // Update validation when calculation data changes
  useEffect(() => {
    if (calculationData) {
      onValidationChange(true, calculationData);
    }
  }, [calculationData, onValidationChange]);

  // Check if validation is needed
  const needsValidation = paymentData.purchaseOrderId ||
                         (paymentData.effectivePaymentTerms !== undefined && paymentData.effectivePaymentTerms !== null);

  // Don't show validation UI for simple supplier payments
  if (!needsValidation) {
    return null;
  }

  if (!paymentData.amount || !paymentData.paymentDate) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Info className="h-4 w-4" />
            <span className="text-sm">Masukkan jumlah dan tanggal pembayaran untuk validasi termin</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isValidating || isCalculating) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
            <span className="text-sm">Memvalidasi termin pembayaran...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!validationResult && !calculationData) {
    return null;
  }

  const calculation = validationResult?.paymentTermsCalculation || calculationData;
  const isValid = validationResult?.valid !== false; // Default to true if no validation result

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          {isValid ? (
            <CheckCircle className="h-5 w-5 text-green-600" />
          ) : (
            <AlertTriangle className="h-5 w-5 text-red-600" />
          )}
          Validasi Termin Pembayaran
        </CardTitle>
        <CardDescription>
          {isValid 
            ? 'Pembayaran sesuai dengan termin yang berlaku'
            : 'Terdapat masalah dengan termin pembayaran'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Validation Status */}
        {validationResult && (
          <Alert variant={isValid ? "default" : "destructive"}>
            <AlertDescription>
              {validationResult.message}
              {validationResult.error && (
                <div className="mt-1 text-sm opacity-90">
                  Detail: {validationResult.error}
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Payment Terms Calculation */}
        {calculation && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Detail Termin Pembayaran</h4>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <span className="text-muted-foreground">Termin Efektif</span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {calculation.effectivePaymentTerms === 0 
                        ? 'Langsung' 
                        : `${calculation.effectivePaymentTerms} hari`
                      }
                    </span>
                    <PaymentTermsSourceBadge source={calculation.source} />
                  </div>
                </div>
                
                <div className="space-y-1">
                  <span className="text-muted-foreground">Tanggal Jatuh Tempo</span>
                  <p className="font-medium">
                    {new Date(calculation.calculatedDueDate).toLocaleDateString('id-ID', {
                      weekday: 'short',
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </p>
                </div>
              </div>

              {/* Overdue Warning */}
              {calculation.isOverdue && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <span>Pembayaran ini sudah terlambat</span>
                      <Badge variant="destructive">
                        {calculation.daysOverdue} hari terlambat
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Early Payment Info */}
              {!calculation.isOverdue && calculation.daysOverdue < 0 && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <span>Pembayaran lebih awal dari jadwal</span>
                      <Badge variant="secondary">
                        {Math.abs(calculation.daysOverdue)} hari lebih awal
                      </Badge>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </>
        )}

        {/* Manual Validation Button */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={validatePaymentTerms}
            disabled={isValidating}
          >
            {isValidating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                Memvalidasi...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Validasi Ulang
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface PaymentTermsSourceBadgeProps {
  source: 'PURCHASE_ORDER' | 'SUPPLIER_DEFAULT' | 'MANUAL';
}

function PaymentTermsSourceBadge({ source }: PaymentTermsSourceBadgeProps) {
  const sourceConfig = {
    PURCHASE_ORDER: { 
      label: 'PO', 
      variant: 'default' as const,
      className: 'bg-blue-100 text-blue-800 hover:bg-blue-100'
    },
    SUPPLIER_DEFAULT: { 
      label: 'Default', 
      variant: 'secondary' as const,
      className: 'bg-gray-100 text-gray-800 hover:bg-gray-100'
    },
    MANUAL: { 
      label: 'Manual', 
      variant: 'outline' as const,
      className: 'bg-purple-100 text-purple-800 hover:bg-purple-100'
    },
  };

  const config = sourceConfig[source];

  return (
    <Badge variant={config.variant} className={`text-xs ${config.className}`}>
      {config.label}
    </Badge>
  );
}
