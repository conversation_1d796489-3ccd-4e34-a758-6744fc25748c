'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CreditCard,
  DollarSign,
  FileText,
  Plus,
  Eye,
  Calendar,
  AlertTriangle,
  RefreshCw,
} from 'lucide-react';
import { PurchaseOrderWithRelations, CreatePurchaseOrderPaymentDto } from '@/types/purchase-order';
import { PaymentStatusBadge } from './PaymentStatusBadge';
import { PaymentSummaryCard } from './PaymentSummaryCard';
import { PaymentInfoDisplay } from './PaymentInfoDisplay';
import {
  usePurchaseOrderPaymentInfo,
  usePurchaseOrderPaymentSummary,
  usePurchaseOrderPaymentSchedule,
  useCreatePurchaseOrderPayment,
  useUpdatePurchaseOrderPayment,
  useDeletePurchaseOrderPayment,
} from '@/hooks/usePurchaseOrders';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';

interface PurchaseOrderPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrderWithRelations | null;
  initialTab?: 'summary' | 'payments' | 'schedule' | 'add';
  initialAction?: 'add' | 'view' | 'edit';
}

export function PurchaseOrderPaymentModal({
  open,
  onOpenChange,
  purchaseOrder,
  initialTab = 'summary',
  initialAction,
}: PurchaseOrderPaymentModalProps) {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPaymentId, setEditingPaymentId] = useState<string | null>(null);
  const [viewingPaymentId, setViewingPaymentId] = useState<string | null>(null);

  // Queries
  const { data: paymentInfo, isLoading: isLoadingPaymentInfo } = usePurchaseOrderPaymentInfo(
    purchaseOrder?.id || ''
  );
  const { data: paymentSummary, isLoading: isLoadingPaymentSummary } = usePurchaseOrderPaymentSummary(
    purchaseOrder?.id || ''
  );
  const { data: paymentSchedule, isLoading: isLoadingPaymentSchedule } = usePurchaseOrderPaymentSchedule(
    purchaseOrder?.id || ''
  );

  // Mutations
  const createPaymentMutation = useCreatePurchaseOrderPayment();
  const updatePaymentMutation = useUpdatePurchaseOrderPayment();
  const deletePaymentMutation = useDeletePurchaseOrderPayment();

  // Handle initial action
  useEffect(() => {
    if (open && initialAction === 'add') {
      setActiveTab('add');
      setShowAddForm(true);
    }
  }, [open, initialAction]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setShowAddForm(false);
      setEditingPaymentId(null);
      setViewingPaymentId(null);
      setActiveTab(initialTab);
    }
  }, [open, initialTab]);

  const handleAddPayment = () => {
    setShowAddForm(true);
    setActiveTab('add');
    setEditingPaymentId(null);
  };

  const handleViewPayment = (paymentId: string) => {
    setViewingPaymentId(paymentId);
    setActiveTab('details');
  };

  const handleEditPayment = (paymentId: string) => {
    setEditingPaymentId(paymentId);
    setShowAddForm(true);
    setActiveTab('add');
  };

  const handleDeletePayment = async (paymentId: string) => {
    if (!purchaseOrder) return;

    try {
      await deletePaymentMutation.mutateAsync({
        id: purchaseOrder.id,
        paymentId,
      });
      toast.success('Pembayaran berhasil dihapus');
    } catch (error) {
      console.error('Failed to delete payment:', error);
      toast.error('Gagal menghapus pembayaran');
    }
  };

  const handleCreatePayment = async (data: CreatePurchaseOrderPaymentDto) => {
    if (!purchaseOrder) return;

    try {
      await createPaymentMutation.mutateAsync({
        id: purchaseOrder.id,
        data,
      });
      setShowAddForm(false);
      setActiveTab('summary');
      toast.success('Pembayaran berhasil dibuat');
    } catch (error) {
      console.error('Failed to create payment:', error);
      toast.error('Gagal membuat pembayaran');
    }
  };

  if (!purchaseOrder) {
    return null;
  }

  const isLoading = isLoadingPaymentInfo || isLoadingPaymentSummary || isLoadingPaymentSchedule;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-6xl max-h-[95vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Manajemen Pembayaran - {purchaseOrder.orderNumber}
          </DialogTitle>
          <DialogDescription>
            Kelola pembayaran untuk purchase order {purchaseOrder.supplier.name}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Ringkasan</TabsTrigger>
            <TabsTrigger value="payments">Riwayat Pembayaran</TabsTrigger>
            <TabsTrigger value="schedule">Jadwal Pembayaran</TabsTrigger>
            <TabsTrigger value="add">Tambah Pembayaran</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid gap-4">
              {/* Purchase Order Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Informasi Purchase Order
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Nomor PO</p>
                      <p className="font-semibold">{purchaseOrder.orderNumber}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Supplier</p>
                      <p className="font-medium">{purchaseOrder.supplier.name}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Total Amount</p>
                      <p className="font-semibold">{formatCurrency(purchaseOrder.totalAmount)}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Status Pembayaran</p>
                      <PaymentStatusBadge status={purchaseOrder.paymentStatus as any} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Summary */}
              {paymentSummary && (
                <PaymentSummaryCard
                  paymentSummary={paymentSummary}
                  onViewPayments={() => setActiveTab('payments')}
                  onAddPayment={handleAddPayment}
                  isLoading={isLoadingPaymentSummary}
                />
              )}

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Aksi Cepat</CardTitle>
                  <CardDescription>
                    Kelola pembayaran dan lihat informasi terkait
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex gap-2 flex-wrap">
                    <Button onClick={handleAddPayment}>
                      <Plus className="h-4 w-4 mr-2" />
                      Tambah Pembayaran
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('payments')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      Lihat Riwayat
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setActiveTab('schedule')}
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Jadwal Pembayaran
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => window.location.reload()}
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Refresh Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="payments" className="space-y-4">
            {paymentInfo ? (
              <PaymentInfoDisplay
                paymentInfo={paymentInfo}
                onViewPayment={handleViewPayment}
                onEditPayment={handleEditPayment}
                onDeletePayment={handleDeletePayment}
                onAddPayment={handleAddPayment}
                isLoading={isLoadingPaymentInfo}
              />
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Memuat informasi pembayaran...</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Jadwal Pembayaran
                </CardTitle>
                <CardDescription>
                  Informasi jadwal dan termin pembayaran
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingPaymentSchedule ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                    <p className="mt-2 text-muted-foreground">Memuat jadwal pembayaran...</p>
                  </div>
                ) : paymentSchedule ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Termin Pembayaran</p>
                        <p className="font-semibold">
                          {paymentSchedule.paymentTerms || 'Tidak ditentukan'} hari
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Jatuh Tempo</p>
                        <p className="font-medium">
                          {paymentSchedule.dueDate 
                            ? new Date(paymentSchedule.dueDate).toLocaleDateString('id-ID')
                            : 'Tidak ditentukan'
                          }
                        </p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Status</p>
                        <PaymentStatusBadge status={paymentSchedule.status as any} />
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Sisa Hari</p>
                        <p className="font-medium">
                          {paymentSchedule.daysRemaining !== undefined 
                            ? `${paymentSchedule.daysRemaining} hari`
                            : 'Tidak ditentukan'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">Jadwal pembayaran tidak tersedia</p>
                    <p className="text-sm">Informasi termin pembayaran belum dikonfigurasi</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="add" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>
                  {editingPaymentId ? 'Edit Pembayaran' : 'Tambah Pembayaran Baru'}
                </CardTitle>
                <CardDescription>
                  {editingPaymentId 
                    ? 'Perbarui informasi pembayaran' 
                    : 'Masukkan detail pembayaran untuk purchase order ini'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Plus className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Form pembayaran akan ditambahkan</p>
                  <p className="text-sm">Komponen form pembayaran sedang dalam pengembangan</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
