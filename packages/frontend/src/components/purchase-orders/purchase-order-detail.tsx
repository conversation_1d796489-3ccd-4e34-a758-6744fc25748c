'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  Edit,
  CheckCircle,
  XCircle,
  Send,
  FileText,
  Printer,
  Package,
  Calendar,
  User,
  Building2,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Calculator,
  Clock,
  AlertTriangle,
  DollarSign,
  Plus,
  Eye,
} from 'lucide-react';
import { PurchaseOrderWithRelations, PurchaseOrderStatus, PaymentStatus } from '@/types/purchase-order';
import {
  getPurchaseOrderStatusLabel,
  getPurchaseOrderStatusColor,
  getPaymentMethodLabel,
  getAllowedNextStatuses,
} from '@/lib/constants/purchase-order';
import { formatCurrency, formatDate, formatDateTime } from '@/lib/utils';
import { getProductCategoryLabel } from '@/lib/constants/product';
import { ProductCategory } from '@prisma/client';
import { PaymentStatusBadge } from './PaymentStatusBadge';
import { PaymentSummaryCard } from './PaymentSummaryCard';
import { usePurchaseOrderPaymentSummary } from '@/hooks/usePurchaseOrders';

interface PurchaseOrderDetailProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onEdit: () => void;
  onCancel: (reason: string) => void;
  onSend: () => void;
  onPrint: () => void;
  onBack: () => void;
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  onViewPaymentSummary?: () => void;
  isLoading?: boolean;
}

export function PurchaseOrderDetail({
  purchaseOrder,
  onEdit,
  onCancel,
  onSend,
  onPrint,
  onBack,
  onViewPayments,
  onAddPayment,
  onViewPaymentSummary,
  isLoading = false,
}: PurchaseOrderDetailProps) {
  const [cancelReason, setCancelReason] = useState('');

  // Payment data
  const { data: paymentSummary, isLoading: isLoadingPaymentSummary } = usePurchaseOrderPaymentSummary(
    purchaseOrder.id
  );

  const allowedNextStatuses = getAllowedNextStatuses(purchaseOrder.status);
  const canEdit = purchaseOrder.status === PurchaseOrderStatus.DRAFT;
  const canSend = purchaseOrder.status === PurchaseOrderStatus.SUBMITTED;
  const cancellableStatuses: PurchaseOrderStatus[] = [
    PurchaseOrderStatus.DRAFT,
    PurchaseOrderStatus.SUBMITTED,
    PurchaseOrderStatus.ORDERED,
    PurchaseOrderStatus.PARTIALLY_RECEIVED,
  ];
  const canCancel = (cancellableStatuses as readonly PurchaseOrderStatus[]).includes(purchaseOrder.status);

  const handleCancel = () => {
    if (cancelReason.trim()) {
      onCancel(cancelReason);
      setCancelReason('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            disabled={isLoading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{purchaseOrder.orderNumber}</h1>
            <p className="text-muted-foreground">
              Purchase Order Detail
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge 
            variant="outline" 
            className={getPurchaseOrderStatusColor(purchaseOrder.status)}
          >
            {getPurchaseOrderStatusLabel(purchaseOrder.status)}
          </Badge>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onPrint}
              disabled={isLoading}
            >
              <Printer className="h-4 w-4 mr-2" />
              Cetak
            </Button>

            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={onEdit}
                disabled={isLoading}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}



            {canSend && (
              <Button size="sm" onClick={onSend} disabled={isLoading}>
                <Send className="h-4 w-4 mr-2" />
                Kirim ke Supplier
              </Button>
            )}

            {canCancel && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm" disabled={isLoading}>
                    <XCircle className="h-4 w-4 mr-2" />
                    Batalkan
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Batalkan Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Apakah Anda yakin ingin membatalkan purchase order ini? Tindakan ini tidak dapat dibatalkan.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <div className="space-y-2">
                    <Label htmlFor="cancel-reason">Alasan Pembatalan *</Label>
                    <Textarea
                      id="cancel-reason"
                      value={cancelReason}
                      onChange={(e) => setCancelReason(e.target.value)}
                      placeholder="Masukkan alasan pembatalan..."
                      rows={3}
                      required
                    />
                  </div>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Batal</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleCancel}
                      disabled={!cancelReason.trim()}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Batalkan Purchase Order
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Purchase Order Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi Purchase Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Nomor PO
                  </Label>
                  <p className="font-medium">{purchaseOrder.orderNumber}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Status
                  </Label>
                  <div className="mt-1">
                    <Badge 
                      variant="outline" 
                      className={getPurchaseOrderStatusColor(purchaseOrder.status)}
                    >
                      {getPurchaseOrderStatusLabel(purchaseOrder.status)}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Tanggal Order
                  </Label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(purchaseOrder.orderDate)}
                  </p>
                </div>
                {purchaseOrder.expectedDelivery && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Estimasi Pengiriman
                    </Label>
                    <p className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {formatDate(purchaseOrder.expectedDelivery)}
                    </p>
                  </div>
                )}
                {purchaseOrder.paymentTerms && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Termin Pembayaran
                    </Label>
                    <p>{purchaseOrder.paymentTerms} hari</p>
                  </div>
                )}
                {purchaseOrder.paymentMethod && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Metode Pembayaran
                    </Label>
                    <p className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      {getPaymentMethodLabel(purchaseOrder.paymentMethod)}
                    </p>
                  </div>
                )}
              </div>

              {purchaseOrder.notes && (
                <>
                  <Separator />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">
                      Catatan
                    </Label>
                    <p className="mt-1 text-sm">{purchaseOrder.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Items Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Item Purchase Order
              </CardTitle>
              <CardDescription>
                {purchaseOrder.items.length} item dalam purchase order ini
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Produk</TableHead>
                      <TableHead>Kuantitas</TableHead>
                      <TableHead>Harga Satuan</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseOrder.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {item.product.code} • {getProductCategoryLabel(item.product.category as ProductCategory)}
                            </div>
                            {item.product.manufacturer && (
                              <div className="text-xs text-muted-foreground">
                                {item.product.manufacturer}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.quantityOrdered} {item.unit.abbreviation}
                            </div>
                            {item.quantityReceived > 0 && (
                              <div className="text-sm text-muted-foreground">
                                Diterima: {item.quantityReceived}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatCurrency(item.unitPrice)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {formatCurrency(item.totalPrice)}
                            </div>
                            {item.discountAmount > 0 && (
                              <div className="text-sm text-green-600">
                                Diskon: -{formatCurrency(item.discountAmount)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {item.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Informasi Supplier
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Nama Supplier
                </Label>
                <p className="font-medium">{purchaseOrder.supplier.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Kode Supplier
                </Label>
                <p className="text-sm font-mono">{purchaseOrder.supplier.code}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Jenis
                </Label>
                <p className="text-sm">{purchaseOrder.supplier.type}</p>
              </div>
              {purchaseOrder.supplier.city && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Kota
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3" />
                    {purchaseOrder.supplier.city}
                  </p>
                </div>
              )}
              {purchaseOrder.supplier.phone && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Telepon
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <Phone className="h-3 w-3" />
                    {purchaseOrder.supplier.phone}
                  </p>
                </div>
              )}
              {purchaseOrder.supplier.email && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Email
                  </Label>
                  <p className="flex items-center gap-2 text-sm">
                    <Mail className="h-3 w-3" />
                    {purchaseOrder.supplier.email}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Ringkasan Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span className="font-medium">{formatCurrency(purchaseOrder.subtotal)}</span>
              </div>

              {purchaseOrder.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Diskon:</span>
                  <span>-{formatCurrency(purchaseOrder.discountAmount)}</span>
                </div>
              )}

              <div className="flex justify-between">
                <span>PPN:</span>
                <span>{formatCurrency(purchaseOrder.taxAmount)}</span>
              </div>

              <Separator />

              <div className="flex justify-between text-lg font-semibold">
                <span>Total:</span>
                <span>{formatCurrency(purchaseOrder.totalAmount)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Payment Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Status Pembayaran
              </CardTitle>
              <CardDescription>
                Informasi pembayaran dan termin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Status Pembayaran
                  </Label>
                  <div className="mt-1">
                    <PaymentStatusBadge status={(purchaseOrder.paymentStatus as PaymentStatus) || 'PENDING'} />
                  </div>
                </div>
                {purchaseOrder.paymentTerms && (
                  <div className="text-right">
                    <Label className="text-sm font-medium text-muted-foreground">
                      Termin Pembayaran
                    </Label>
                    <p className="font-medium">{purchaseOrder.paymentTerms} hari</p>
                  </div>
                )}
              </div>

              {paymentSummary && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Dibayar:</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(paymentSummary.paidAmount)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Sisa:</span>
                    <span className="font-medium text-blue-600">
                      {formatCurrency(paymentSummary.remainingAmount)}
                    </span>
                  </div>
                  {paymentSummary.isOverdue && paymentSummary.overdueAmount > 0 && (
                    <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-sm">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <span className="text-red-800">
                        Terlambat: {formatCurrency(paymentSummary.overdueAmount)}
                      </span>
                    </div>
                  )}
                </div>
              )}

              <Separator />

              <div className="flex gap-2">
                {onViewPaymentSummary && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onViewPaymentSummary}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Lihat Ringkasan
                  </Button>
                )}
                {onAddPayment && (
                  <Button
                    size="sm"
                    onClick={onAddPayment}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Pembayaran
                  </Button>
                )}
              </div>

              {onViewPayments && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onViewPayments}
                  className="w-full"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Kelola Pembayaran
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Audit Trail */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Riwayat
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Dibuat
                </Label>
                <p className="text-sm">
                  {formatDateTime(purchaseOrder.createdAt)}
                  {purchaseOrder.createdByUser && (
                    <span className="block text-muted-foreground">
                      oleh {purchaseOrder.createdByUser.firstName} {purchaseOrder.createdByUser.lastName}
                    </span>
                  )}
                </p>
              </div>





              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Terakhir Diperbarui
                </Label>
                <p className="text-sm">
                  {formatDateTime(purchaseOrder.updatedAt)}
                  {purchaseOrder.updatedByUser && (
                    <span className="block text-muted-foreground">
                      oleh {purchaseOrder.updatedByUser.firstName} {purchaseOrder.updatedByUser.lastName}
                    </span>
                  )}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
