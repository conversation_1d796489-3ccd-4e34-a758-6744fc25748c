'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Loader2, DollarSign, CreditCard } from 'lucide-react';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { CreatePurchaseOrderPaymentDto, PurchaseOrderWithRelations } from '@/types/purchase-order';
import { PaymentMethod, PaymentStatus } from '@prisma/client';
import { PAYMENT_METHOD_OPTIONS, PAYMENT_STATUS_OPTIONS } from '@/lib/constants/supplier';
import { formatCurrency } from '@/lib/utils';

// Form validation schema
const paymentFormSchema = z.object({
  amount: z.number().min(0.01, 'Jumlah pembayaran harus lebih dari 0'),
  paymentMethod: z.nativeEnum(PaymentMethod, {
    required_error: 'Metode pembayaran harus dipilih',
  }),
  paymentDate: z.date({
    required_error: 'Tanggal pembayaran harus diisi',
  }),
  dueDate: z.date().optional(),
  status: z.nativeEnum(PaymentStatus).optional(),
  reference: z.string().optional(),
  notes: z.string().optional(),
  invoiceNumber: z.string().optional(),
  effectivePaymentTerms: z.number().optional(),
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface PurchaseOrderPaymentFormProps {
  purchaseOrder: PurchaseOrderWithRelations;
  initialData?: Partial<CreatePurchaseOrderPaymentDto>;
  onSubmit: (data: CreatePurchaseOrderPaymentDto) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  isEditing?: boolean;
}

export function PurchaseOrderPaymentForm({
  purchaseOrder,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEditing = false,
}: PurchaseOrderPaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amount: initialData?.amount || 0,
      paymentMethod: initialData?.paymentMethod || PaymentMethod.TRANSFER,
      paymentDate: initialData?.paymentDate ? new Date(initialData.paymentDate) : new Date(),
      dueDate: initialData?.dueDate ? new Date(initialData.dueDate) : undefined,
      status: initialData?.status || PaymentStatus.PAID,
      reference: initialData?.reference || '',
      notes: initialData?.notes || '',
      invoiceNumber: initialData?.invoiceNumber || '',
      effectivePaymentTerms: initialData?.effectivePaymentTerms || purchaseOrder.paymentTerms || undefined,
    },
  });

  // Calculate suggested amount (remaining amount)
  const totalAmount = Number(purchaseOrder.totalAmount);
  const paidAmount = 0; // TODO: Get from payment summary
  const remainingAmount = totalAmount - paidAmount;

  // Auto-fill amount with remaining amount if not editing
  useEffect(() => {
    if (!isEditing && remainingAmount > 0) {
      form.setValue('amount', remainingAmount);
    }
  }, [form, isEditing, remainingAmount]);

  // Auto-calculate due date based on payment terms
  const handlePaymentDateChange = (date: Date | undefined) => {
    if (date && purchaseOrder.paymentTerms) {
      const dueDate = new Date(date);
      dueDate.setDate(dueDate.getDate() + purchaseOrder.paymentTerms);
      form.setValue('dueDate', dueDate);
    }
  };

  const handleSubmit = async (data: PaymentFormData) => {
    setIsSubmitting(true);
    try {
      const submitData: CreatePurchaseOrderPaymentDto = {
        purchaseOrderId: purchaseOrder.id,
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        paymentDate: data.paymentDate.toISOString(),
        dueDate: data.dueDate?.toISOString(),
        status: data.status,
        reference: data.reference || undefined,
        notes: data.notes || undefined,
        invoiceNumber: data.invoiceNumber || undefined,
        effectivePaymentTerms: data.effectivePaymentTerms,
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Payment form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          {isEditing ? 'Edit Pembayaran' : 'Tambah Pembayaran Baru'}
        </CardTitle>
        <CardDescription>
          {isEditing 
            ? 'Perbarui informasi pembayaran untuk purchase order ini'
            : `Masukkan detail pembayaran untuk PO ${purchaseOrder.orderNumber}`
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Purchase Order Summary */}
        <div className="mb-6 p-4 bg-muted rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Total PO</p>
              <p className="font-semibold">{formatCurrency(totalAmount)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Sudah Dibayar</p>
              <p className="font-semibold text-green-600">{formatCurrency(paidAmount)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Sisa</p>
              <p className="font-semibold text-blue-600">{formatCurrency(remainingAmount)}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Supplier</p>
              <p className="font-semibold">{purchaseOrder.supplier.name}</p>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Amount and Payment Method Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Jumlah Pembayaran *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          className="pl-10"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Sisa yang belum dibayar: {formatCurrency(remainingAmount)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Metode Pembayaran *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih metode pembayaran" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_METHOD_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Payment Date and Due Date Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="paymentDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Tanggal Pembayaran *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP', { locale: idLocale })
                            ) : (
                              <span>Pilih tanggal</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            field.onChange(date);
                            handlePaymentDateChange(date);
                          }}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Tanggal Jatuh Tempo</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP', { locale: idLocale })
                            ) : (
                              <span>Pilih tanggal (opsional)</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date('1900-01-01')}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      {purchaseOrder.paymentTerms && 
                        `Otomatis dihitung berdasarkan termin ${purchaseOrder.paymentTerms} hari`
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Status and Reference Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status Pembayaran</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_STATUS_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reference"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nomor Referensi</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor transaksi/referensi"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Nomor transaksi bank, cek, atau referensi lainnya
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Invoice Number */}
            <FormField
              control={form.control}
              name="invoiceNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nomor Invoice</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nomor invoice dari supplier"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catatan</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Catatan tambahan untuk pembayaran ini..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? 'Memperbarui...' : 'Menyimpan...'}
                  </>
                ) : (
                  <>
                    <CreditCard className="mr-2 h-4 w-4" />
                    {isEditing ? 'Perbarui Pembayaran' : 'Simpan Pembayaran'}
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting || isLoading}
              >
                Batal
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
