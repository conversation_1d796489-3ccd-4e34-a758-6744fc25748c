# 🔌 Payment Integration API Documentation

## 🎯 Overview
Comprehensive API documentation for the payment management integration across purchase orders and goods receipts.

## 📋 API Endpoints

### Purchase Order Payment APIs

#### Get Payment Summary
```http
GET /api/purchase-orders/{id}/payment-summary
```

**Response:**
```typescript
interface PurchaseOrderPaymentSummary {
  purchaseOrderId: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentStatus: PaymentStatus;
  paymentCount: number;
  lastPaymentDate?: string;
  nextDueDate?: string;
  overdueAmount: number;
  isOverdue: boolean;
}
```

#### Get Payment Schedule
```http
GET /api/purchase-orders/{id}/payment-schedule
```

**Response:**
```typescript
interface PaymentSchedule {
  purchaseOrderId: string;
  paymentTerms: number;
  dueDate: string;
  status: PaymentStatus;
  daysRemaining: number;
  scheduledPayments: ScheduledPayment[];
}
```

#### Get Payment Info
```http
GET /api/purchase-orders/{id}/payment-info
```

**Response:**
```typescript
interface PurchaseOrderPaymentInfo {
  id: string;
  orderNumber: string;
  supplierId: string;
  supplierName: string;
  totalAmount: number;
  paymentStatus: PaymentStatus;
  paymentTerms?: number;
  orderDate: string;
  dueDate?: string;
  paymentSummary: PurchaseOrderPaymentSummary;
  payments: SupplierPaymentWithRelations[];
}
```

#### Get Payments
```http
GET /api/purchase-orders/{id}/payments?page=1&limit=10&status=PENDING
```

**Query Parameters:**
- `page` (number): Page number for pagination
- `limit` (number): Number of items per page
- `status` (PaymentStatus): Filter by payment status
- `paymentMethod` (PaymentMethod): Filter by payment method
- `search` (string): Search term
- `dateFrom` (string): Start date filter
- `dateTo` (string): End date filter

**Response:**
```typescript
interface PaymentListResponse {
  data: SupplierPaymentWithRelations[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### Create Payment
```http
POST /api/purchase-orders/{id}/payments
```

**Request Body:**
```typescript
interface CreatePurchaseOrderPaymentDto {
  purchaseOrderId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  dueDate?: string;
  effectivePaymentTerms?: number;
  status?: PaymentStatus;
  reference?: string;
  notes?: string;
  invoiceNumber?: string;
}
```

#### Update Payment
```http
PATCH /api/purchase-orders/{id}/payments/{paymentId}
```

**Request Body:** Partial `CreatePurchaseOrderPaymentDto`

#### Delete Payment
```http
DELETE /api/purchase-orders/{id}/payments/{paymentId}
```

---

### Goods Receipt Payment APIs

#### Get Payment Summary
```http
GET /api/goods-receipts/{id}/payment-summary
```

**Response:**
```typescript
interface GoodsReceiptPaymentSummary {
  goodsReceiptId: string;
  purchaseOrderId: string;
  totalAmount: number;
  deliveryConfirmedAmount: number;
  pendingPaymentAmount: number;
  paymentStatus: PaymentStatus;
  deliveryDate: string;
  calculatedDueDate: string;
  invoiceMatched: boolean;
  invoiceNumber?: string;
}
```

#### Get Delivery Payment Calculation
```http
GET /api/goods-receipts/{id}/delivery-payment-calculation
```

**Response:**
```typescript
interface DeliveryBasedPayment {
  id: string;
  goodsReceiptId: string;
  purchaseOrderId: string;
  deliveryDate: string;
  originalDueDate: string;
  recalculatedDueDate: string;
  paymentTerms: number;
  amount: number;
  status: 'PENDING_DELIVERY' | 'DELIVERY_CONFIRMED' | 'PAYMENT_DUE' | 'PAID';
}
```

#### Trigger Delivery Payment
```http
POST /api/goods-receipts/{id}/trigger-payment
```

**Request Body:**
```typescript
interface TriggerPaymentDto {
  deliveryConfirmed: boolean;
  deliveryDate: string;
  amount: number;
  paymentMethod: PaymentMethod;
  notes?: string;
  invoiceNumber?: string;
}
```

#### Match Invoice
```http
POST /api/goods-receipts/{id}/match-invoice
```

**Request Body:**
```typescript
interface InvoiceMatchingData {
  invoiceNumber: string;
  invoiceDate: string;
  invoiceAmount: number;
  goodsReceiptAmount: number;
  matchingStatus: 'PENDING' | 'MATCHED' | 'DISCREPANCY' | 'REJECTED';
  discrepancyReason?: string;
}
```

#### Get Delivery Payment Schedule
```http
GET /api/goods-receipts/{id}/delivery-payment-schedule
```

---

### Unified Payment APIs

#### Get Cross-Context Payments
```http
GET /api/payments/unified?entityType=PURCHASE_ORDER&entityId={id}
```

**Query Parameters:**
- `entityType` (string): 'PURCHASE_ORDER' | 'GOODS_RECEIPT'
- `entityId` (string): Entity ID
- `context` (string): Payment context filter
- `dateRange` (string): Date range filter

#### Get Payment Context
```http
GET /api/payments/{paymentId}/context
```

**Response:**
```typescript
interface UnifiedPaymentContext {
  sourceType: 'PURCHASE_ORDER' | 'GOODS_RECEIPT';
  sourceId: string;
  sourceName: string;
  paymentType: 'ADVANCE' | 'PARTIAL' | 'FINAL' | 'BALANCE';
  paymentCategory: 'COMMITMENT' | 'DELIVERY' | 'COMPLETION';
  businessStage: 'PLANNING' | 'EXECUTION' | 'COMPLETION';
  workflowStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED';
  dueDateBasis: 'ORDER_DATE' | 'DELIVERY_DATE';
  effectiveDate: string;
  calculatedDueDate: string;
  relatedEntities: {
    purchaseOrderId?: string;
    goodsReceiptId?: string;
    supplierId: string;
    supplierName: string;
  };
}
```

---

## 🔧 React Query Hooks

### Purchase Order Payment Hooks

```typescript
// Payment summary
export function usePurchaseOrderPaymentSummary(id: string);

// Payment schedule
export function usePurchaseOrderPaymentSchedule(id: string);

// Payment list
export function usePurchaseOrderPayments(id: string, params?: PurchaseOrderPaymentQueryParams);

// Payment info
export function usePurchaseOrderPaymentInfo(id: string);

// Mutations
export function useCreatePurchaseOrderPayment();
export function useUpdatePurchaseOrderPayment();
export function useDeletePurchaseOrderPayment();
```

### Goods Receipt Payment Hooks

```typescript
// Payment summary
export function useGoodsReceiptPaymentSummary(id: string);

// Delivery payment calculation
export function useDeliveryPaymentCalculation(id: string);

// Trigger payment
export function useTriggerDeliveryPayment();

// Invoice matching
export function useInvoiceMatching();

// Delivery payment schedule
export function useDeliveryPaymentSchedule(id: string);
```

### Unified Payment Hooks

```typescript
// Cross-context payments
export function useUnifiedPayments(context: UnifiedPaymentContext);

// Payment context
export function usePaymentContext(paymentId: string);

// Unified operations
export function useCreateContextualPayment();
export function useUpdatePaymentContext();
```

---

## 🔒 Authentication & Authorization

### Required Permissions

#### Purchase Order Payments
- **View**: `purchase-orders:read` + `payments:read`
- **Create**: `purchase-orders:write` + `payments:create`
- **Update**: `purchase-orders:write` + `payments:update`
- **Delete**: `purchase-orders:write` + `payments:delete`

#### Goods Receipt Payments
- **View**: `goods-receipts:read` + `payments:read`
- **Create**: `goods-receipts:write` + `payments:create`
- **Update**: `goods-receipts:write` + `payments:update`
- **Delete**: `goods-receipts:write` + `payments:delete`

### Role-Based Access

| Role | Purchase Order Payments | Goods Receipt Payments | Unified Dashboard |
|------|------------------------|----------------------|-------------------|
| **Admin** | Full Access | Full Access | Full Access |
| **Pharmacist** | Full Access | Full Access | Read + Limited Write |
| **Staff** | Read Only | Read Only | Read Only |
| **Viewer** | No Access | No Access | No Access |

---

## 📊 Error Handling

### Standard Error Response
```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
  };
}
```

### Common Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `PAYMENT_NOT_FOUND` | Payment not found | 404 |
| `INVALID_PAYMENT_AMOUNT` | Invalid payment amount | 400 |
| `PAYMENT_ALREADY_PROCESSED` | Payment already processed | 409 |
| `INSUFFICIENT_PERMISSIONS` | Insufficient permissions | 403 |
| `VALIDATION_ERROR` | Request validation failed | 400 |
| `BUSINESS_RULE_VIOLATION` | Business rule violation | 422 |

---

## 🧪 Testing

### API Testing
- Unit tests for all endpoints
- Integration tests for workflows
- Performance tests for large datasets
- Security tests for authorization

### Hook Testing
- React Query hook testing
- Error state testing
- Loading state testing
- Cache invalidation testing

---

**Next**: [Component Documentation](../components/README.md) | [Testing Documentation](../testing/README.md)
