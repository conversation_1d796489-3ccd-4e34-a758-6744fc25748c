# 💳 Simple Payment API Documentation

## 🎯 Overview
**Simplified** payment API documentation for Indonesian pharmacy store - following your existing backend patterns.

## 📋 **Core Payment APIs** (Keep It Simple!)

### **Purchase Order Payments** (Extend existing controller)

#### Add to existing `purchase-order.controller.ts`:
```typescript
// Just add these 3 simple endpoints to your existing controller:

@Get(':id/payment-summary')
async getPaymentSummary(@Param('id') id: string) {
  return this.purchaseOrderService.getPaymentSummary(id);
}

@Get(':id/payments')
async getPayments(@Param('id') id: string, @Query() query: any) {
  return this.purchaseOrderService.getPayments(id, query);
}

@Post(':id/payments')
async createPayment(@Param('id') id: string, @Body() data: any, @Request() req) {
  return this.purchaseOrderService.createPayment(id, data, req.user.id);
}
```

#### **Simple Response Types:**
```typescript
// Reuse your existing PaymentSchedule from PaymentTermsService
interface SimplePaymentSummary {
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentStatus: 'PENDING' | 'PARTIAL' | 'PAID' | 'OVERDUE';
  dueDate?: string;
  isOverdue: boolean;
}

// Reuse existing SupplierPayment from your database
interface PaymentListResponse {
  data: SupplierPayment[];  // Your existing Prisma type
  total: number;
}
```

### **Goods Receipt Payments** (Optional - Only if needed)

#### Add to existing `goods-receipt.controller.ts`:
```typescript
// Only add if you really need goods receipt payments
// Most pharmacies can manage payments from purchase orders only

@Get(':id/payment-status')
async getPaymentStatus(@Param('id') id: string) {
  return this.goodsReceiptService.getPaymentStatus(id);
}

@Post(':id/confirm-delivery')
async confirmDelivery(@Param('id') id: string, @Body() data: any) {
  return this.goodsReceiptService.confirmDelivery(id, data);
}
```

#### **Simple Goods Receipt Payment:**
```typescript
interface SimpleGRPayment {
  deliveryConfirmed: boolean;
  deliveryDate?: string;
  paymentDueDate?: string;  // Recalculated based on delivery
  invoiceNumber?: string;
}
```

---

## 🎯 **Recommended Approach: Start Simple!**

### **Phase 1: Purchase Order Payments Only**
```typescript
// Just extend your existing purchase-order.service.ts
class PurchaseOrderService {

  // Add these 3 simple methods:
  async getPaymentSummary(id: string) {
    // Use your existing PaymentTermsService
    return this.paymentTermsService.getPaymentSchedule(id);
  }

  async getPayments(id: string, query: any) {
    // Query existing supplier_payments table
    return this.prisma.supplierPayment.findMany({
      where: { purchaseOrderId: id },
      include: { supplier: true }
    });
  }

  async createPayment(id: string, data: any, userId: string) {
    // Use existing supplier payment creation
    return this.prisma.supplierPayment.create({
      data: { ...data, purchaseOrderId: id, createdBy: userId }
    });
  }
}
```

### **Phase 2: Add Goods Receipt (Later, if needed)**
Only add goods receipt payments if your pharmacy actually needs delivery-based payment processing.

---

## 🔧 **Simple React Query Hooks** (Follow your existing patterns)

### **Purchase Order Payment Hooks** (Keep it simple!)

```typescript
// Follow your existing hook patterns like useProducts, useCustomers
export function usePurchaseOrderPaymentSummary(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payment-summary'],
    queryFn: () => purchaseOrdersApi.getPaymentSummary(id),
    enabled: !!id,
  });
}

export function usePurchaseOrderPayments(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payments'],
    queryFn: () => purchaseOrdersApi.getPayments(id),
    enabled: !!id,
  });
}

export function useCreatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      purchaseOrdersApi.createPayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil dibuat');
    },
  });
}
```

### **Optional: Goods Receipt Hooks** (Only if you implement GR payments)

```typescript
// Only add these if you actually need goods receipt payments
export function useGoodsReceiptPaymentStatus(id: string) {
  return useQuery({
    queryKey: ['goods-receipts', id, 'payment-status'],
    queryFn: () => goodsReceiptsApi.getPaymentStatus(id),
    enabled: !!id,
  });
}

export function useConfirmDelivery() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      goodsReceiptsApi.confirmDelivery(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['goods-receipts'] });
      toast.success('Pengiriman berhasil dikonfirmasi');
    },
  });
}
```

---

## 🔒 **Simple Authentication** (Use your existing guards)

```typescript
// Just use your existing guards - no need to overcomplicate!
@UseGuards(JwtAuthGuard)           // For authentication
@UseGuards(ManagerGuard)           // For admin/pharmacist only actions
```

### **Role Access** (Keep it simple)
- **Admin/Pharmacist**: Can create, view, edit payments
- **Cashier**: Can view payments only
- **Others**: No payment access

---

## 📊 **Simple Error Handling** (Follow your existing patterns)

```typescript
// Use your existing error handling patterns
try {
  const payment = await this.createPayment(data);
  return payment;
} catch (error) {
  this.logger.error('Failed to create payment', error);
  throw new BadRequestException('Gagal membuat pembayaran');
}
```

---

## 🎯 **Key Recommendation: Start with Purchase Orders Only!**

### **Why Keep It Simple:**
1. **Your pharmacy is small** - complex payment workflows may be overkill
2. **Purchase orders are the main payment trigger** - most payments happen when you order from suppliers
3. **You already have payment infrastructure** - PaymentTermsService, SupplierPayment table
4. **Easy to extend later** - if you need goods receipt payments, add them incrementally

### **Implementation Priority:**
1. ✅ **Start**: Add 3 simple endpoints to existing purchase-order.controller.ts
2. ✅ **Then**: Create simple payment UI components
3. ✅ **Later**: Add goods receipt payments only if actually needed
4. ❌ **Skip**: Complex unified payment dashboard (unless you have hundreds of suppliers)

### **Database Changes Needed:**
```sql
-- Minimal changes to your existing schema
ALTER TABLE supplier_payments
ADD COLUMN purchase_order_id VARCHAR(36),
ADD FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id);

-- That's it! Use your existing tables.
```
| `BUSINESS_RULE_VIOLATION` | Business rule violation | 422 |

---

**This simplified approach will give you 80% of the payment functionality with 20% of the complexity!**

**Perfect for an Indonesian pharmacy store - no over-engineering needed! 🎯**

## 🧪 Testing

### API Testing
- Unit tests for all endpoints
- Integration tests for workflows
- Performance tests for large datasets
- Security tests for authorization

### Hook Testing
- React Query hook testing
- Error state testing
- Loading state testing
- Cache invalidation testing

---

**Next**: [Component Documentation](../components/README.md) | [Testing Documentation](../testing/README.md)
