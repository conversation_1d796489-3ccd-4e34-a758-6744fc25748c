# 🚚 Phase 2: Goods Receipt Payment Integration

## 🎯 Objective
Implement comprehensive payment management for goods receipts with delivery-based payment processing, following Indonesian pharmacy business practices where payments are triggered upon delivery confirmation.

## 📋 Tasks Overview

### Task 1: Extend Goods Receipt Types with Payment Data
**Duration**: 1 day | **Priority**: Critical | **Complexity**: Low

#### Description
Add payment-related interfaces and types to goods receipt system for delivery-based payment processing.

#### Deliverables
- Enhanced `GoodsReceiptWithRelations` interface
- Delivery-based payment interfaces
- Invoice matching type definitions
- Payment trigger data structures

#### Technical Details
```typescript
// New interfaces to add
interface GoodsReceiptPaymentSummary {
  goodsReceiptId: string;
  purchaseOrderId: string;
  totalAmount: number;
  deliveryConfirmedAmount: number;
  pendingPaymentAmount: number;
  paymentStatus: PaymentStatus;
  deliveryDate: string;
  calculatedDueDate: string;
  invoiceMatched: boolean;
  invoiceNumber?: string;
}

interface DeliveryBasedPayment {
  id: string;
  goodsReceiptId: string;
  purchaseOrderId: string;
  deliveryDate: string;
  originalDueDate: string;
  recalculatedDueDate: string;
  paymentTerms: number;
  amount: number;
  status: 'PENDING_DELIVERY' | 'DELIVERY_CONFIRMED' | 'PAYMENT_DUE' | 'PAID';
}

interface InvoiceMatchingData {
  invoiceNumber: string;
  invoiceDate: string;
  invoiceAmount: number;
  goodsReceiptAmount: number;
  matchingStatus: 'PENDING' | 'MATCHED' | 'DISCREPANCY' | 'REJECTED';
  discrepancyReason?: string;
  matchedBy?: string;
  matchedAt?: string;
}
```

#### Files to Modify
- `packages/frontend/src/types/goods-receipt.ts`
- `packages/frontend/src/types/supplier.ts`

#### Acceptance Criteria
- [ ] All new interfaces properly typed
- [ ] Integration with existing GR types
- [ ] Payment relationship definitions clear
- [ ] No TypeScript errors

---

### Task 2: Create Goods Receipt Payment API Integration
**Duration**: 2-3 days | **Priority**: Critical | **Complexity**: Medium

#### Description
Implement API endpoints and hooks for goods receipt payment management, including delivery-based payment triggers.

#### Deliverables
- Goods receipt payment API endpoints
- React Query hooks for GR payments
- Delivery-based payment calculations
- Invoice matching API integration

#### Technical Details
```typescript
// API endpoints to implement
const goodsReceiptPaymentApi = {
  // Get payment summary for goods receipt
  getPaymentSummary: (grId: string) => Promise<GoodsReceiptPaymentSummary>;
  
  // Get delivery-based payment calculations
  getDeliveryPaymentCalculation: (grId: string) => Promise<DeliveryBasedPayment>;
  
  // Trigger payment processing upon delivery confirmation
  triggerDeliveryPayment: (grId: string, data: TriggerPaymentDto) => Promise<SupplierPayment>;
  
  // Invoice matching operations
  matchInvoice: (grId: string, invoiceData: InvoiceMatchingData) => Promise<InvoiceMatchingResult>;
  
  // Get payment schedule based on delivery
  getDeliveryPaymentSchedule: (grId: string) => Promise<PaymentSchedule>;
};

// React Query hooks
export function useGoodsReceiptPaymentSummary(grId: string);
export function useDeliveryPaymentCalculation(grId: string);
export function useTriggerDeliveryPayment();
export function useInvoiceMatching();
```

#### Files to Create
- `packages/frontend/src/lib/api/goods-receipt-payments.ts`
- `packages/frontend/src/hooks/useGoodsReceiptPayments.ts`

#### Backend Integration
- Add endpoints to goods receipt controller
- Implement delivery-based payment calculation service
- Create invoice matching service
- Add payment trigger functionality

#### Acceptance Criteria
- [ ] All API endpoints functional
- [ ] React Query hooks working
- [ ] Error handling implemented
- [ ] Loading states managed
- [ ] Backend integration complete

---

### Task 3: Develop Goods Receipt Payment Components
**Duration**: 3-4 days | **Priority**: High | **Complexity**: High

#### Description
Create specialized payment components for goods receipts including delivery confirmation payments and invoice matching.

#### Deliverables
- `GoodsReceiptPaymentSummary` component
- `DeliveryPaymentTrigger` component
- `InvoiceMatchingInterface` component
- `DeliveryPaymentStatus` component

#### Technical Details
```typescript
// Component specifications
interface GoodsReceiptPaymentSummaryProps {
  goodsReceipt: GoodsReceiptWithRelations;
  paymentSummary: GoodsReceiptPaymentSummary;
  onTriggerPayment: () => void;
  onMatchInvoice: () => void;
  isLoading?: boolean;
}

interface DeliveryPaymentTriggerProps {
  goodsReceipt: GoodsReceiptWithRelations;
  deliveryPayment: DeliveryBasedPayment;
  onConfirmDelivery: (data: DeliveryConfirmationDto) => void;
  onTriggerPayment: (data: TriggerPaymentDto) => void;
  disabled?: boolean;
}

interface InvoiceMatchingInterfaceProps {
  goodsReceipt: GoodsReceiptWithRelations;
  onMatchInvoice: (data: InvoiceMatchingData) => void;
  onRejectInvoice: (reason: string) => void;
  existingMatch?: InvoiceMatchingData;
  isLoading?: boolean;
}
```

#### Design Requirements
- Context-specific UI for goods receipts
- Clear distinction from purchase order payment UI
- Delivery confirmation workflow
- Invoice matching interface
- Payment trigger controls

#### Files to Create
- `packages/frontend/src/components/goods-receipts/GoodsReceiptPaymentSummary.tsx`
- `packages/frontend/src/components/goods-receipts/DeliveryPaymentTrigger.tsx`
- `packages/frontend/src/components/goods-receipts/InvoiceMatchingInterface.tsx`
- `packages/frontend/src/components/goods-receipts/DeliveryPaymentStatus.tsx`

#### Acceptance Criteria
- [ ] All components render correctly
- [ ] Delivery workflow functional
- [ ] Invoice matching working
- [ ] Payment triggers operational
- [ ] Error handling comprehensive

---

### Task 4: Implement Delivery-Based Payment Calculator
**Duration**: 2 days | **Priority**: High | **Complexity**: Medium

#### Description
Create component for recalculating payment due dates based on actual delivery dates, following Indonesian pharmacy practices.

#### Deliverables
- Delivery date-based due date calculator
- Payment terms recalculation logic
- Business day calculation (Indonesian calendar)
- Payment schedule adjustment interface

#### Technical Details
```typescript
// Calculator specifications
interface DeliveryPaymentCalculator {
  calculateDueDate: (deliveryDate: string, paymentTerms: number) => string;
  adjustForBusinessDays: (date: string) => string;
  calculatePaymentSchedule: (delivery: DeliveryBasedPayment) => PaymentSchedule;
  validatePaymentTerms: (terms: number) => ValidationResult;
}

// Indonesian business calendar considerations
const INDONESIAN_HOLIDAYS = [
  // National holidays that affect payment calculations
];

const BUSINESS_DAY_RULES = {
  excludeWeekends: true,
  excludeHolidays: true,
  adjustToNextBusinessDay: true,
};
```

#### Business Logic
- Due date = Delivery date + Payment terms (in business days)
- Exclude Indonesian national holidays
- Exclude weekends (Saturday, Sunday)
- Adjust to next business day if due date falls on non-business day

#### Files to Create
- `packages/frontend/src/lib/utils/delivery-payment-calculator.ts`
- `packages/frontend/src/components/goods-receipts/DeliveryPaymentCalculator.tsx`

#### Acceptance Criteria
- [ ] Due date calculation accurate
- [ ] Business day logic working
- [ ] Holiday exclusion functional
- [ ] Payment schedule generation correct
- [ ] Validation rules enforced

---

### Task 5: Integrate Payment Management into Goods Receipt Table
**Duration**: 1-2 days | **Priority**: Medium | **Complexity**: Medium

#### Description
Add payment status and action buttons to goods receipt data table with context-specific functionality.

#### Deliverables
- Payment status column in GR table
- Context-specific payment actions
- Delivery payment indicators
- Invoice matching status display

#### Technical Details
```typescript
// Table column additions
const goodsReceiptPaymentColumns = [
  {
    accessorKey: 'paymentStatus',
    header: 'Status Pembayaran',
    cell: ({ row }) => (
      <GoodsReceiptPaymentStatusCell 
        goodsReceipt={row.original}
        context="GOODS_RECEIPT"
      />
    ),
  },
  {
    accessorKey: 'deliveryPaymentStatus',
    header: 'Status Pengiriman',
    cell: ({ row }) => (
      <DeliveryPaymentStatusCell 
        goodsReceipt={row.original}
      />
    ),
  },
];
```

#### Files to Modify
- `packages/frontend/src/components/goods-receipts/columns.tsx`
- `packages/frontend/src/components/goods-receipts/GoodsReceiptsPageClient.tsx`

#### Acceptance Criteria
- [ ] Payment columns added to table
- [ ] Context-specific actions working
- [ ] Delivery status indicators functional
- [ ] Invoice matching status visible
- [ ] Navigation to payment management working

---

### Task 6: Create Goods Receipt Payment Modal
**Duration**: 2-3 days | **Priority**: High | **Complexity**: High

#### Description
Develop specialized payment modal for goods receipts focused on payment execution and processing.

#### Deliverables
- Goods receipt-specific payment modal
- Delivery confirmation interface
- Invoice matching workflow
- Payment processing controls

#### Technical Details
```typescript
// Modal specifications
interface GoodsReceiptPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  goodsReceipt: GoodsReceiptWithRelations | null;
  initialTab?: 'delivery' | 'invoice' | 'payment' | 'schedule';
  initialAction?: 'confirm' | 'match' | 'process';
}

// Tab structure
const GR_PAYMENT_MODAL_TABS = {
  delivery: 'Konfirmasi Pengiriman',
  invoice: 'Pencocokan Invoice',
  payment: 'Proses Pembayaran',
  schedule: 'Jadwal Pembayaran',
};
```

#### Key Differences from PO Payment Modal
- Focus on delivery confirmation
- Invoice matching workflow
- Payment processing (not planning)
- Delivery-based due date calculation

#### Files to Create
- `packages/frontend/src/components/goods-receipts/GoodsReceiptPaymentModal.tsx`

#### Acceptance Criteria
- [ ] Modal renders correctly
- [ ] Tab navigation functional
- [ ] Delivery confirmation working
- [ ] Invoice matching operational
- [ ] Payment processing functional

---

### Task 7: Integrate Payment Management into Goods Receipt Detail Page
**Duration**: 1-2 days | **Priority**: Medium | **Complexity**: Medium

#### Description
Add payment processing section to goods receipt detail page with delivery-based payment triggers.

#### Deliverables
- Payment section in GR detail page
- Delivery confirmation controls
- Invoice matching interface
- Payment trigger buttons

#### Files to Modify
- `packages/frontend/src/components/goods-receipts/goods-receipt-detail.tsx`
- `packages/frontend/src/components/goods-receipts/GoodsReceiptDetailPageClient.tsx`

#### Acceptance Criteria
- [ ] Payment section added to detail page
- [ ] Delivery controls functional
- [ ] Invoice matching accessible
- [ ] Payment triggers working
- [ ] Navigation to modal operational

---

### Task 8: Implement Invoice Matching Interface
**Duration**: 2-3 days | **Priority**: Medium | **Complexity**: High

#### Description
Create interface for matching supplier invoices with goods receipts for payment processing.

#### Deliverables
- Invoice upload and parsing
- Automatic matching logic
- Manual matching interface
- Discrepancy resolution workflow

#### Technical Details
```typescript
// Invoice matching workflow
interface InvoiceMatchingWorkflow {
  uploadInvoice: (file: File) => Promise<ParsedInvoice>;
  parseInvoiceData: (invoice: ParsedInvoice) => InvoiceData;
  autoMatch: (invoice: InvoiceData, gr: GoodsReceipt) => MatchingResult;
  manualMatch: (invoice: InvoiceData, gr: GoodsReceipt) => MatchingResult;
  resolveDiscrepancy: (discrepancy: Discrepancy) => Resolution;
}
```

#### Files to Create
- `packages/frontend/src/components/goods-receipts/InvoiceUploadInterface.tsx`
- `packages/frontend/src/components/goods-receipts/InvoiceMatchingWorkflow.tsx`
- `packages/frontend/src/components/goods-receipts/DiscrepancyResolution.tsx`

#### Acceptance Criteria
- [ ] Invoice upload functional
- [ ] Automatic matching working
- [ ] Manual matching interface operational
- [ ] Discrepancy resolution workflow complete
- [ ] Error handling comprehensive

---

### Task 9: Add Backend Goods Receipt Payment Endpoints
**Duration**: 2-3 days | **Priority**: Critical | **Complexity**: Medium

#### Description
Implement missing backend API endpoints for goods receipt payment management.

#### Deliverables
- GR payment summary endpoint
- Delivery payment calculation endpoint
- Invoice matching endpoints
- Payment trigger endpoints

#### Files to Modify
- `packages/backend/src/procurement/goods-receipt.controller.ts`
- `packages/backend/src/procurement/services/goods-receipt.service.ts`

#### Acceptance Criteria
- [ ] All endpoints implemented
- [ ] Proper authentication/authorization
- [ ] Error handling comprehensive
- [ ] Data validation working
- [ ] Integration tests passing

## 🔧 Technical Implementation Guide

### Development Workflow
1. Start with type definitions
2. Implement API layer
3. Create core components
4. Add table integration
5. Implement modal interface
6. Add detail page integration
7. Create specialized interfaces
8. Complete backend endpoints

### Key Architectural Decisions
- **Context Separation**: Clear distinction between PO and GR payment contexts
- **Delivery Focus**: All GR payments based on delivery confirmation
- **Invoice Integration**: Mandatory invoice matching for payment processing
- **Business Logic**: Indonesian business day calculations

### Performance Considerations
- Lazy load payment data
- Cache delivery calculations
- Optimize invoice matching algorithms
- Implement proper pagination

## 📊 Success Criteria

### Functional Requirements
- [ ] Delivery-based payment calculation working
- [ ] Invoice matching functional
- [ ] Payment processing operational
- [ ] Integration with existing systems complete

### Business Requirements
- [ ] Indonesian pharmacy practices supported
- [ ] Delivery confirmation workflow functional
- [ ] Payment terms compliance maintained
- [ ] Invoice verification process working

## 🧪 Testing Strategy

### Unit Tests
- Component rendering tests
- API hook tests
- Utility function tests
- Validation logic tests

### Integration Tests
- Payment workflow tests
- Invoice matching tests
- Delivery confirmation tests
- Cross-component interaction tests

### E2E Tests
- Complete payment processing workflow
- Invoice matching end-to-end
- Delivery confirmation to payment
- Error scenario handling

---

**Next Phase**: [Phase 3: Unified Payment Dashboard](../phase-3/README.md)
