# 🧩 Payment Integration Component Documentation

## 🎯 Overview
Comprehensive documentation for all payment-related components across the purchase order and goods receipt integration.

## 📋 Component Categories

### Core Payment Components
- Payment status indicators
- Payment summary displays
- Payment action controls
- Payment form components

### Purchase Order Payment Components
- PO-specific payment interfaces
- Payment commitment tracking
- Advance payment workflows
- Payment planning components

### Goods Receipt Payment Components
- GR-specific payment interfaces
- Delivery confirmation components
- Invoice matching interfaces
- Payment processing controls

### Unified Payment Components
- Cross-context payment displays
- Unified payment dashboard
- Payment analytics components
- Compliance monitoring interfaces

---

## 🔧 Core Payment Components

### PaymentStatusBadge

**Purpose**: Display payment status with consistent styling across contexts

```typescript
interface PaymentStatusBadgeProps {
  status: PaymentStatus;
  context?: 'PURCHASE_ORDER' | 'GOODS_RECEIPT' | 'UNIFIED';
  className?: string;
  showTooltip?: boolean;
}
```

**Usage:**
```tsx
<PaymentStatusBadge 
  status="PARTIAL" 
  context="PURCHASE_ORDER"
  showTooltip={true}
/>
```

**Features:**
- Context-aware styling
- Tooltip with detailed information
- Consistent color scheme
- Accessibility support

---

### PaymentSummaryCard

**Purpose**: Display comprehensive payment summary information

```typescript
interface PaymentSummaryCardProps {
  paymentSummary: PaymentSummary;
  context: 'PURCHASE_ORDER' | 'GOODS_RECEIPT';
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  isLoading?: boolean;
  className?: string;
}
```

**Features:**
- Payment progress visualization
- Amount breakdowns
- Overdue warnings
- Quick action buttons
- Loading skeleton states

---

### PaymentActionButtons

**Purpose**: Provide context-specific payment actions

```typescript
interface PaymentActionButtonsProps {
  entity: PurchaseOrderWithRelations | GoodsReceiptWithRelations;
  context: 'PURCHASE_ORDER' | 'GOODS_RECEIPT';
  onViewPayments?: () => void;
  onAddPayment?: () => void;
  onViewPaymentSummary?: () => void;
  compact?: boolean;
  showDropdown?: boolean;
}
```

**Features:**
- Context-aware actions
- Compact and full view modes
- Dropdown menu integration
- Permission-based visibility

---

## 🛒 Purchase Order Payment Components

### PurchaseOrderPaymentModal

**Purpose**: Comprehensive payment management for purchase orders

```typescript
interface PurchaseOrderPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrderWithRelations | null;
  initialTab?: 'summary' | 'payments' | 'schedule' | 'add';
  initialAction?: 'add' | 'view' | 'edit';
}
```

**Tabs:**
- **Summary**: Payment overview and quick actions
- **Payments**: Payment history and management
- **Schedule**: Payment schedule and terms
- **Add**: Create new payments

**Features:**
- Tabbed interface
- Payment commitment tracking
- Advance payment workflows
- Payment planning tools

---

### PaymentCommitmentCard

**Purpose**: Track and manage payment commitments

```typescript
interface PaymentCommitmentCardProps {
  commitments: PaymentCommitment[];
  onCreateCommitment: (commitment: CreateCommitmentDto) => void;
  onUpdateCommitment: (id: string, updates: Partial<PaymentCommitment>) => void;
  onDeleteCommitment: (id: string) => void;
  isLoading?: boolean;
}
```

**Features:**
- Commitment timeline
- Status tracking
- Commitment creation/editing
- Progress visualization

---

### AdvancePaymentSelector

**Purpose**: Configure and select advance payment options

```typescript
interface AdvancePaymentSelectorProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onSelectAdvanceOption: (option: AdvancePaymentOption) => void;
  availableOptions: AdvancePaymentOption[];
  selectedOption?: AdvancePaymentOption;
}
```

**Features:**
- Predefined advance options
- Custom amount selection
- Terms calculation
- Validation feedback

---

## 🚚 Goods Receipt Payment Components

### GoodsReceiptPaymentModal

**Purpose**: Payment processing for goods receipts

```typescript
interface GoodsReceiptPaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  goodsReceipt: GoodsReceiptWithRelations | null;
  initialTab?: 'delivery' | 'invoice' | 'payment' | 'schedule';
  initialAction?: 'confirm' | 'match' | 'process';
}
```

**Tabs:**
- **Delivery**: Delivery confirmation
- **Invoice**: Invoice matching
- **Payment**: Payment processing
- **Schedule**: Payment schedule

**Features:**
- Delivery-based payment triggers
- Invoice matching workflow
- Payment processing controls
- Due date recalculation

---

### DeliveryPaymentTrigger

**Purpose**: Trigger payments upon delivery confirmation

```typescript
interface DeliveryPaymentTriggerProps {
  goodsReceipt: GoodsReceiptWithRelations;
  deliveryPayment: DeliveryBasedPayment;
  onConfirmDelivery: (data: DeliveryConfirmationDto) => void;
  onTriggerPayment: (data: TriggerPaymentDto) => void;
  disabled?: boolean;
}
```

**Features:**
- Delivery confirmation workflow
- Automatic due date calculation
- Payment trigger controls
- Validation and error handling

---

### InvoiceMatchingInterface

**Purpose**: Match supplier invoices with goods receipts

```typescript
interface InvoiceMatchingInterfaceProps {
  goodsReceipt: GoodsReceiptWithRelations;
  onMatchInvoice: (data: InvoiceMatchingData) => void;
  onRejectInvoice: (reason: string) => void;
  existingMatch?: InvoiceMatchingData;
  isLoading?: boolean;
}
```

**Features:**
- Invoice upload and parsing
- Automatic matching logic
- Manual matching interface
- Discrepancy resolution

---

### DeliveryPaymentCalculator

**Purpose**: Calculate payment due dates based on delivery

```typescript
interface DeliveryPaymentCalculatorProps {
  deliveryDate: string;
  paymentTerms: number;
  onCalculate: (result: PaymentCalculationResult) => void;
  showBusinessDays?: boolean;
}
```

**Features:**
- Business day calculations
- Indonesian holiday support
- Terms validation
- Due date visualization

---

## 🎛️ Unified Payment Components

### UnifiedPaymentDashboard

**Purpose**: Cross-context payment overview and management

```typescript
interface UnifiedPaymentDashboardProps {
  dateRange: DateRange;
  filters: PaymentDashboardFilters;
  onFilterChange: (filters: PaymentDashboardFilters) => void;
  onDateRangeChange: (range: DateRange) => void;
}
```

**Features:**
- Multi-entity payment view
- Interactive filtering
- Real-time updates
- Export capabilities

---

### ContextAwarePaymentBadge

**Purpose**: Payment status display with context awareness

```typescript
interface ContextAwarePaymentBadgeProps {
  status: PaymentStatus;
  context: UnifiedPaymentContext;
  showContext?: boolean;
  onClick?: () => void;
}
```

**Features:**
- Context indicators
- Source entity display
- Click navigation
- Consistent styling

---

### CrossContextPaymentActions

**Purpose**: Unified payment actions across contexts

```typescript
interface CrossContextPaymentActionsProps {
  payment: SupplierPaymentWithRelations;
  context: UnifiedPaymentContext;
  onAction: (action: PaymentAction) => void;
  availableActions: PaymentAction[];
}
```

**Features:**
- Context-specific actions
- Permission-based filtering
- Unified action handling
- Consistent UI patterns

---

## 🎨 Design System Integration

### Color Scheme
```scss
// Payment status colors
$payment-pending: #fbbf24;    // Yellow
$payment-partial: #3b82f6;    // Blue
$payment-paid: #10b981;       // Green
$payment-overdue: #ef4444;    // Red
$payment-cancelled: #6b7280;  // Gray
```

### Typography
- **Headers**: Inter Semi-Bold 16px
- **Body**: Inter Regular 14px
- **Captions**: Inter Regular 12px
- **Labels**: Inter Medium 14px

### Spacing
- **Card padding**: 16px
- **Button spacing**: 8px gap
- **Section spacing**: 24px
- **Component spacing**: 12px

### Icons
- **Payment**: CreditCard, DollarSign
- **Actions**: Plus, Eye, Edit, Trash2
- **Status**: CheckCircle, XCircle, Clock, AlertTriangle

---

## 🧪 Component Testing

### Testing Strategy
```typescript
// Component testing example
describe('PaymentStatusBadge', () => {
  it('renders correct status label', () => {
    render(<PaymentStatusBadge status="PAID" />);
    expect(screen.getByText('Lunas')).toBeInTheDocument();
  });

  it('applies correct styling for status', () => {
    render(<PaymentStatusBadge status="OVERDUE" />);
    expect(screen.getByText('Terlambat')).toHaveClass('bg-red-100');
  });

  it('shows tooltip when enabled', async () => {
    render(<PaymentStatusBadge status="PARTIAL" showTooltip={true} />);
    fireEvent.mouseOver(screen.getByText('Sebagian'));
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });
  });
});
```

### Testing Requirements
- [ ] Unit tests for all components
- [ ] Integration tests for workflows
- [ ] Accessibility tests
- [ ] Visual regression tests
- [ ] Performance tests

---

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Mobile Adaptations
- Stacked layouts for payment cards
- Collapsible sections
- Touch-friendly buttons
- Simplified navigation

### Tablet Adaptations
- Grid layouts for payment summaries
- Side-by-side modals
- Optimized spacing
- Enhanced touch targets

---

## ♿ Accessibility

### WCAG 2.1 AA Compliance
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Color contrast compliance
- [ ] Focus management
- [ ] ARIA labels and descriptions

### Implementation
```tsx
// Accessibility example
<button
  aria-label="Tambah pembayaran untuk purchase order"
  aria-describedby="payment-help-text"
  onClick={onAddPayment}
>
  <Plus className="h-4 w-4" aria-hidden="true" />
  Tambah Pembayaran
</button>
```

---

**Next**: [Testing Documentation](../testing/README.md) | [Deployment Guide](../deployment/README.md)
