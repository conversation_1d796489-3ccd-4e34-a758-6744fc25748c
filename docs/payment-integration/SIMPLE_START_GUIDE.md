# 🚀 Simple Payment Integration - Quick Start Guide

## 🎯 **Perfect for Indonesian Pharmacy Store**

This guide shows you how to add payment management to your pharmacy app **without overcomplicating** your existing system.

## ✅ **What You Already Have (Great!)**

- ✅ Purchase Order system working
- ✅ Supplier management with PaymentTermsService
- ✅ Database with supplier_payments table
- ✅ Simple, clean controller patterns
- ✅ Authentication with JwtAuthGuard and ManagerGuard

## 🎯 **What We'll Add (Simple!)**

- 💳 Payment summary for purchase orders
- 📋 Payment list and creation
- 🎨 Simple payment UI components
- 📊 Basic payment status tracking

---

## 🗄️ **Step 1: Minimal Database Changes**

```sql
-- Just add one foreign key to your existing table
ALTER TABLE supplier_payments 
ADD COLUMN purchase_order_id VARCHAR(36),
ADD FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id);

-- Add index for performance
CREATE INDEX idx_supplier_payments_po_id ON supplier_payments(purchase_order_id);

-- That's it! No complex new tables needed.
```

---

## 🔧 **Step 2: Extend Existing Controller (3 endpoints)**

Add these to your existing `purchase-order.controller.ts`:

```typescript
// Add these imports
import { PaymentTermsService } from '../suppliers/services/payment-terms.service';

// Inject the service you already have
constructor(
  private readonly purchaseOrderService: PurchaseOrderService,
  private readonly paymentTermsService: PaymentTermsService, // Add this
) {}

// Add these 3 simple endpoints:

@Get(':id/payment-summary')
@UseGuards(ManagerGuard)
async getPaymentSummary(@Param('id') id: string) {
  return this.paymentTermsService.getPaymentSchedule(id);
}

@Get(':id/payments')
@UseGuards(ManagerGuard)
async getPayments(@Param('id') id: string) {
  return this.purchaseOrderService.getPayments(id);
}

@Post(':id/payments')
@UseGuards(ManagerGuard)
async createPayment(
  @Param('id') id: string, 
  @Body() data: CreateSupplierPaymentDto,
  @Request() req
) {
  return this.purchaseOrderService.createPayment(id, data, req.user.id);
}
```

---

## 🔧 **Step 3: Extend Existing Service**

Add these methods to your existing `purchase-order.service.ts`:

```typescript
// Add these simple methods:

async getPayments(purchaseOrderId: string) {
  return this.prisma.supplierPayment.findMany({
    where: { purchaseOrderId },
    include: {
      supplier: true,
      createdByUser: { select: { name: true } }
    },
    orderBy: { paymentDate: 'desc' }
  });
}

async createPayment(purchaseOrderId: string, data: any, userId: string) {
  // Get purchase order to get supplier info
  const purchaseOrder = await this.findOne(purchaseOrderId);
  
  return this.prisma.supplierPayment.create({
    data: {
      ...data,
      purchaseOrderId,
      supplierId: purchaseOrder.supplierId,
      createdBy: userId,
    },
    include: {
      supplier: true,
      createdByUser: { select: { name: true } }
    }
  });
}
```

---

## 🎨 **Step 4: Simple Frontend Components**

### **4.1: Payment Status Badge**
```typescript
// packages/frontend/src/components/purchase-orders/PaymentStatusBadge.tsx
interface PaymentStatusBadgeProps {
  status: 'PENDING' | 'PARTIAL' | 'PAID' | 'OVERDUE';
}

export function PaymentStatusBadge({ status }: PaymentStatusBadgeProps) {
  const config = {
    PENDING: { label: 'Menunggu', className: 'bg-yellow-100 text-yellow-800' },
    PARTIAL: { label: 'Sebagian', className: 'bg-blue-100 text-blue-800' },
    PAID: { label: 'Lunas', className: 'bg-green-100 text-green-800' },
    OVERDUE: { label: 'Terlambat', className: 'bg-red-100 text-red-800' },
  }[status];

  return (
    <Badge className={config.className}>
      {config.label}
    </Badge>
  );
}
```

### **4.2: Simple Payment Summary**
```typescript
// packages/frontend/src/components/purchase-orders/PaymentSummary.tsx
interface PaymentSummaryProps {
  purchaseOrder: PurchaseOrderWithRelations;
  onAddPayment: () => void;
}

export function PaymentSummary({ purchaseOrder, onAddPayment }: PaymentSummaryProps) {
  const { data: paymentSummary } = usePurchaseOrderPaymentSummary(purchaseOrder.id);
  
  if (!paymentSummary) return <div>Loading...</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status Pembayaran</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Total:</span>
            <span>{formatCurrency(paymentSummary.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span>Dibayar:</span>
            <span className="text-green-600">{formatCurrency(paymentSummary.totalPaid)}</span>
          </div>
          <div className="flex justify-between">
            <span>Sisa:</span>
            <span className="text-blue-600">{formatCurrency(paymentSummary.remainingAmount)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Status:</span>
            <PaymentStatusBadge status={paymentSummary.paymentStatus} />
          </div>
        </div>
        
        <Button onClick={onAddPayment} className="w-full mt-4">
          <Plus className="h-4 w-4 mr-2" />
          Tambah Pembayaran
        </Button>
      </CardContent>
    </Card>
  );
}
```

### **4.3: Simple Payment Modal**
```typescript
// packages/frontend/src/components/purchase-orders/PaymentModal.tsx
interface PaymentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  purchaseOrder: PurchaseOrderWithRelations;
}

export function PaymentModal({ open, onOpenChange, purchaseOrder }: PaymentModalProps) {
  const { data: payments } = usePurchaseOrderPayments(purchaseOrder.id);
  const createPayment = useCreatePurchaseOrderPayment();
  
  const handleSubmit = (data: any) => {
    createPayment.mutate({ id: purchaseOrder.id, data });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Kelola Pembayaran - {purchaseOrder.orderNumber}</DialogTitle>
        </DialogHeader>
        
        <Tabs defaultValue="summary">
          <TabsList>
            <TabsTrigger value="summary">Ringkasan</TabsTrigger>
            <TabsTrigger value="payments">Riwayat</TabsTrigger>
            <TabsTrigger value="add">Tambah</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summary">
            <PaymentSummary 
              purchaseOrder={purchaseOrder} 
              onAddPayment={() => {/* switch to add tab */}} 
            />
          </TabsContent>
          
          <TabsContent value="payments">
            {/* Simple payment list */}
            <div className="space-y-2">
              {payments?.map(payment => (
                <div key={payment.id} className="flex justify-between p-2 border rounded">
                  <span>{formatCurrency(payment.amount)}</span>
                  <span>{new Date(payment.paymentDate).toLocaleDateString('id-ID')}</span>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="add">
            {/* Simple payment form */}
            <PaymentForm onSubmit={handleSubmit} />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
```

---

## 🔧 **Step 5: Simple API Hooks**

```typescript
// packages/frontend/src/hooks/usePurchaseOrderPayments.ts
export function usePurchaseOrderPaymentSummary(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payment-summary'],
    queryFn: () => purchaseOrdersApi.getPaymentSummary(id),
    enabled: !!id,
  });
}

export function usePurchaseOrderPayments(id: string) {
  return useQuery({
    queryKey: ['purchase-orders', id, 'payments'],
    queryFn: () => purchaseOrdersApi.getPayments(id),
    enabled: !!id,
  });
}

export function useCreatePurchaseOrderPayment() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      purchaseOrdersApi.createPayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['purchase-orders'] });
      toast.success('Pembayaran berhasil dibuat');
    },
  });
}
```

---

## 🎯 **Step 6: Add to Purchase Order Table**

Add payment column to your existing purchase order table:

```typescript
// In your purchase order columns
{
  accessorKey: 'paymentStatus',
  header: 'Status Pembayaran',
  cell: ({ row }) => {
    const po = row.original;
    return <PaymentStatusBadge status={po.paymentStatus || 'PENDING'} />;
  },
}
```

Add payment action to dropdown:

```typescript
// In your purchase order actions dropdown
<DropdownMenuItem onClick={() => openPaymentModal(purchaseOrder)}>
  <CreditCard className="mr-2 h-4 w-4" />
  Kelola Pembayaran
</DropdownMenuItem>
```

---

## ✅ **That's It! Simple & Effective**

### **What You Get:**
- ✅ Payment tracking for purchase orders
- ✅ Simple payment creation and viewing
- ✅ Payment status in purchase order table
- ✅ Indonesian pharmacy-friendly interface
- ✅ Uses your existing infrastructure

### **What You Avoid:**
- ❌ Complex unified payment systems
- ❌ Over-engineered goods receipt payments
- ❌ Unnecessary database complexity
- ❌ Advanced analytics you don't need

### **Perfect for:**
- 🏪 Small to medium pharmacy stores
- 🇮🇩 Indonesian business practices
- 💰 Simple supplier payment tracking
- 📋 Purchase order-based payment workflow

---

**Start with this simple approach - you can always add more complexity later if needed!** 🎯
